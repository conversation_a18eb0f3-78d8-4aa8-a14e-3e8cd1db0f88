#property strict

#include "interface/IMartingaleGroupManager.mqh"
#include "../mql4-lib-master/Collection/HashMap.mqh"
#include "../mql4-lib-master/Collection/Vector.mqh"

//+------------------------------------------------------------------+
//| 馬丁格爾訂單組管理器實現                                         |
//| 管理多個馬丁格爾訂單組，提供統一的操作介面                       |
//+------------------------------------------------------------------+
class MartingaleGroupManager : public IMartingaleGroupManager
{
private:
   HashMap<string, MartingaleOrderGroup*> m_groups;  // 訂單組存儲容器

public:
   //+------------------------------------------------------------------+
   //| 建構函數                                                          |
   //| 初始化訂單組管理器，設置為擁有模式以自動管理記憶體                |
   //+------------------------------------------------------------------+
   MartingaleGroupManager() : m_groups(NULL, true) {}

   //+------------------------------------------------------------------+
   //| 解構函數                                                          |
   //| 清理所有訂單組資源                                                |
   //+------------------------------------------------------------------+
   ~MartingaleGroupManager()
   {
      ClearAllGroups();
   }

   //+------------------------------------------------------------------+
   //| 添加訂單組                                                        |
   //| @param id - 訂單組唯一識別碼                                     |
   //| @param group - 要添加的訂單組指標                                |
   //| @return true 如果成功添加，false 如果ID已存在或參數無效         |
   //+------------------------------------------------------------------+
   virtual bool SetGroup(string id, MartingaleOrderGroup* group) override
   {
      if (group == NULL || id == "")
         return false;

      // 如果ID已存在，先移除舊的
      if (m_groups.contains(id))
      {
         MartingaleOrderGroup* oldGroup = m_groups[id];
         delete oldGroup;
      }

      m_groups.set(id, group);
      return true;
   }

   //+------------------------------------------------------------------+
   //| 刪除訂單組                                                        |
   //| @param id - 要刪除的訂單組ID                                     |
   //| @return true 如果成功刪除，false 如果ID不存在                   |
   //+------------------------------------------------------------------+
   virtual bool RemoveGroup(string id) override
   {
      if (!m_groups.contains(id))
         return false;

      MartingaleOrderGroup* group = m_groups[id];
      if (group != NULL)
      {
         delete group;
      }

      return m_groups.remove(id);
   }

   //+------------------------------------------------------------------+
   //| 根據ID獲取訂單組                                                 |
   //| @param id - 訂單組ID                                            |
   //| @return 訂單組指標，如果不存在則返回NULL                        |
   //+------------------------------------------------------------------+
   virtual MartingaleOrderGroup* GetGroup(string id) override
   {
      if (!m_groups.contains(id))
         return NULL;

      return m_groups[id];
   }

   //+------------------------------------------------------------------+
   //| 關閉所有訂單組                                                    |
   //| 關閉所有訂單組中的所有訂單                                        |
   //| @return true 如果所有訂單組都成功關閉，false 如果有任何失敗      |
   //+------------------------------------------------------------------+
   virtual bool CloseAllGroups() override
   {
      bool allSuccess = true;

      foreachm(string, id, MartingaleOrderGroup*, group, m_groups)
      {
         if (group != NULL)
         {
            if (!group.closeAll())
            {
               allSuccess = false;
            }
         }
      }

      return allSuccess;
   }

   //+------------------------------------------------------------------+
   //| 更新所有訂單組狀態                                                |
   //| 刷新所有訂單組的狀態信息                                          |
   //+------------------------------------------------------------------+
   virtual void UpdateAllGroups(int &tickets[]) override
   {
      foreachm(string, id, MartingaleOrderGroup*, group, m_groups)
      {
         if (group != NULL)
         {
            group.clearClosed();
         }
      }
   }

   //+------------------------------------------------------------------+
   //| 獲取總盈虧                                                        |
   //| 計算所有訂單組的總盈虧                                            |
   //| @return 所有訂單組的總盈虧金額                                   |
   //+------------------------------------------------------------------+
   virtual double GetTotalProfit() override
   {
      double totalProfit = 0.0;

      foreachm(string, id, MartingaleOrderGroup*, group, m_groups)
      {
         if (group != NULL)
         {
            totalProfit += group.groupNetProfit();
         }
      }

      return totalProfit;
   }

   //+------------------------------------------------------------------+
   //| 獲取訂單組總數                                                    |
   //| @return 當前管理的訂單組數量                                     |
   //+------------------------------------------------------------------+
   virtual int GetGroupCount() override
   {
      return m_groups.size();
   }

   //+------------------------------------------------------------------+
   //| 檢查訂單組是否存在                                                |
   //| @param groupId - 訂單組ID（應該是string類型）                   |
   //| @return true 如果訂單組存在，否則false                          |
   //+------------------------------------------------------------------+
   virtual bool GroupExists(string groupId) override
   {
      return m_groups.contains(groupId);
   }

   //+------------------------------------------------------------------+
   //| 清空所有訂單組                                                    |
   //| 刪除所有訂單組並釋放記憶體                                        |
   //+------------------------------------------------------------------+
   virtual void ClearAllGroups() override
   {
      // HashMap的解構函數會自動刪除所有值（因為owned=true）
      m_groups.clear();
   }

   //+------------------------------------------------------------------+
   //| 獲取管理器統計信息                                                |
   //| @return 包含統計信息的字符串                                     |
   //+------------------------------------------------------------------+
   virtual string GetStatistics() override
   {
      string stats = "";
      stats += "馬丁格爾訂單組管理器統計:\n";
      stats += "總訂單組數: " + IntegerToString(GetGroupCount()) + "\n";
      stats += "總盈虧: " + DoubleToString(GetTotalProfit(), 2) + "\n";

      int totalOrders = 0;
      foreachm(string, id, MartingaleOrderGroup*, group, m_groups)
      {
         if (group != NULL)
         {
            totalOrders += group.groupOrderTotal();
         }
      }

      stats += "總訂單數: " + IntegerToString(totalOrders) + "\n";

      return stats;
   }

   //+------------------------------------------------------------------+
   //| 設置所有訂單組的止盈                                              |
   //| @param profit - 止盈金額                                         |
   //+------------------------------------------------------------------+
   virtual void SetAllGroupsTP(double profit) override
   {
      foreachm(string, id, MartingaleOrderGroup*, group, m_groups)
      {
         if (group != NULL)
         {
            // 為訂單組中的每個訂單設置止盈
            for (int i = 0; i < group.size(); i++)
            {
               int ticket = group.get(i);
               if (OrderSelect(ticket, SELECT_BY_TICKET))
               {
                  double currentPrice = (OrderType() == OP_BUY) ? Bid : Ask;
                  double tpPrice = (OrderType() == OP_BUY) ?
                                   OrderOpenPrice() + profit * Point :
                                   OrderOpenPrice() - profit * Point;

                  if(!OrderModify(ticket, OrderOpenPrice(), OrderStopLoss(), tpPrice, 0))
                  {
                     int err = GetLastError();
                     Alert(">>> Error modifying order #", ticket, ": ", GetErrorDescription(err));
                  }
               }
            }
         }
      }
   }

   //+------------------------------------------------------------------+
   //| 設置所有訂單組的止損                                              |
   //| @param loss - 止損金額                                           |
   //+------------------------------------------------------------------+
   virtual void SetAllGroupsSL(double loss) override
   {
      foreachm(string, id, MartingaleOrderGroup*, group, m_groups)
      {
         if (group != NULL)
         {
            // 為訂單組中的每個訂單設置止損
            for (int i = 0; i < group.size(); i++)
            {
               int ticket = group.get(i);
               if (OrderSelect(ticket, SELECT_BY_TICKET))
               {
                  double currentPrice = (OrderType() == OP_BUY) ? Bid : Ask;
                  double slPrice = (OrderType() == OP_BUY) ?
                                   OrderOpenPrice() - loss * Point :
                                   OrderOpenPrice() + loss * Point;

                  if(!OrderModify(ticket, OrderOpenPrice(), slPrice, OrderTakeProfit(), 0))
                  {
                     int err = GetLastError();
                     Alert(">>> Error modifying order #", ticket, ": ", GetErrorDescription(err));
                  }
               }
            }
         }
      }
   }
};
