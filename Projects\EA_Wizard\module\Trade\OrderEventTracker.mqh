//+------------------------------------------------------------------+
//| OrderEventTracker.mqh                                            |
//| 訂單事件追蹤器模組                                                |
//| 提供訂單狀態變化的事件驅動追蹤功能                                |
//+------------------------------------------------------------------+
#property strict

#include "../mql4-lib-master/Collection/Vector.mqh"
#include "../mql4-lib-master/Trade/Order.mqh"
#include "../mql4-lib-master/Trade/OrderTracker.mqh"

//+------------------------------------------------------------------+
//| 訂單追蹤事件類型枚舉                                              |
//| 定義所有可能的訂單狀態變化事件                                    |
//+------------------------------------------------------------------+
enum OrderTrackedType
{
    ORDER_TRACKED_NONE,              // 無事件（空事件或錯誤狀態）
    ORDER_TRACKED_NEW,               // 新訂單建立事件
    ORDER_TRACKED_ACTIVATED,         // 掛單激活事件（掛單轉為市價單）
    ORDER_TRACKED_MODIFIED,          // 訂單修改事件（止損、止盈、價格等變更）
    ORDER_TRACKED_CLOSED,            // 訂單關閉事件
    ORDER_TRACKED_PARTIALLY_CLOSED   // 訂單部分關閉事件
};

//+------------------------------------------------------------------+
//| 訂單事件類                                                        |
//| 封裝單一訂單事件，包含事件類型和相關訂單資訊                      |
//+------------------------------------------------------------------+
class OrderEvent
{
private:
    OrderTrackedType m_type;         // 事件類型
    Order* m_order;                  // 相關訂單指標

public:
    //+------------------------------------------------------------------+
    //| 建構函數                                                          |
    //| @param type - 事件類型                                           |
    //| @param order - 相關訂單指標                                      |
    //+------------------------------------------------------------------+
    OrderEvent(OrderTrackedType type, const Order* order) : m_type(type), m_order((Order*)order) {}

    //+------------------------------------------------------------------+
    //| 取得事件類型                                                      |
    //| @return 事件類型枚舉值                                           |
    //+------------------------------------------------------------------+
    OrderTrackedType getType() const { return m_type; }

    //+------------------------------------------------------------------+
    //| 取得相關訂單                                                      |
    //| @return 訂單指標                                                 |
    //+------------------------------------------------------------------+
    Order* getOrder() const { return m_order; }
};

//+------------------------------------------------------------------+
//| 訂單事件佇列類                                                    |
//| 實現先進先出（FIFO）的事件佇列，用於管理訂單事件的順序處理        |
//+------------------------------------------------------------------+
class OrderEventQueue
{
private:
    Vector<OrderEvent*> m_queue;     // 事件佇列容器

public:
    //+------------------------------------------------------------------+
    //| 預設建構函數                                                      |
    //+------------------------------------------------------------------+
    OrderEventQueue(){}

    //+------------------------------------------------------------------+
    //| 將事件加入佇列尾端                                                |
    //| @param event - 要加入的事件指標                                  |
    //+------------------------------------------------------------------+
    void Enqueue(OrderEvent* event)
    {
        m_queue.push(event);
    }

    //+------------------------------------------------------------------+
    //| 從佇列前端取出事件                                                |
    //| @return 事件指標，若佇列為空則返回空事件                         |
    //+------------------------------------------------------------------+
    OrderEvent* dequeue()
    {
        if (m_queue.isEmpty())
            return new OrderEvent(ORDER_TRACKED_NONE, NULL);

        return m_queue.shift();
    }

    //+------------------------------------------------------------------+
    //| 查看佇列前端事件但不移除                                          |
    //| @return 事件指標，若佇列為空則返回空事件                         |
    //+------------------------------------------------------------------+
    OrderEvent* peek() const
    {
        if (m_queue.isEmpty())
            return new OrderEvent(ORDER_TRACKED_NONE, NULL);

        return m_queue[0];
    }

    //+------------------------------------------------------------------+
    //| 取得佇列大小                                                      |
    //| @return 佇列中事件的數量                                         |
    //+------------------------------------------------------------------+
    int size() const { return m_queue.size(); }

    //+------------------------------------------------------------------+
    //| 檢查佇列是否為空                                                  |
    //| @return true 如果佇列為空，否則 false                           |
    //+------------------------------------------------------------------+
    bool isEmpty() const { return m_queue.isEmpty(); }

    //+------------------------------------------------------------------+
    //| 清空佇列                                                          |
    //+------------------------------------------------------------------+
    void clear() { m_queue.clear(); }

    //+------------------------------------------------------------------+
    //| 檢查佇列是否包含特定事件                                          |
    //| @param event - 要檢查的事件指標                                  |
    //| @return true 如果佇列包含該事件，否則 false                     |
    //+------------------------------------------------------------------+
    bool contains(OrderEvent* event) const
    {
        for (int i = 0; i < m_queue.size(); i++)
        {
            if (m_queue[i] == event)
                return true;
        }
        return false;
    }
};

//+------------------------------------------------------------------+
//| 訂單事件追蹤器類                                                  |
//| 繼承自 OrderTracker，提供事件驅動的訂單狀態變化追蹤功能           |
//| 將訂單狀態變化轉換為事件並存儲在內部事件容器中                    |
//+------------------------------------------------------------------+
class OrderEventTracker : public OrderTracker
{
private:
    Vector<OrderEvent*> m_orders;    // 內部事件存儲容器

public:
    //+------------------------------------------------------------------+
    //| 建構函數                                                          |
    //| @param pool - 要追蹤的交易池指標                                 |
    //+------------------------------------------------------------------+
    OrderEventTracker(TradingPool* pool) : OrderTracker(pool) {}

    //+------------------------------------------------------------------+
    //| 追蹤開始時的回調函數                                              |
    //| 清空之前的事件記錄，準備新一輪的追蹤                              |
    //+------------------------------------------------------------------+
    virtual void onStart() override
    {
        m_orders.clear();
    }

    //+------------------------------------------------------------------+
    //| 訂單變更時的回調函數                                              |
    //| 當訂單的止損、止盈、價格或到期時間發生變化時觸發                  |
    //| @param oldOrder - 變更前的訂單狀態                               |
    //| @param newOrder - 變更後的訂單狀態                               |
    //+------------------------------------------------------------------+
    virtual void onChange(const Order *oldOrder, const Order *newOrder) override
    {
        m_orders.push(new OrderEvent(ORDER_TRACKED_MODIFIED, newOrder));
    }

    //+------------------------------------------------------------------+
    //| 新訂單建立時的回調函數                                            |
    //| 當有新的訂單被建立時觸發                                          |
    //| @param order - 新建立的訂單                                      |
    //+------------------------------------------------------------------+
    virtual void onNew(const Order *order) override
    {
        m_orders.push(new OrderEvent(ORDER_TRACKED_NEW, order));
    }

    //+------------------------------------------------------------------+
    //| 掛單激活時的回調函數                                              |
    //| 當掛單被激活轉為市價單時觸發                                      |
    //| @param pendingOrder - 原掛單                                     |
    //| @param marketOrder - 激活後的市價單                              |
    //+------------------------------------------------------------------+
    virtual void onActivation(const Order *pendingOrder, const Order *marketOrder) override
    {
        m_orders.push(new OrderEvent(ORDER_TRACKED_ACTIVATED, marketOrder));
    }

    //+------------------------------------------------------------------+
    //| 訂單關閉時的回調函數                                              |
    //| 當訂單被完全關閉時觸發                                            |
    //| @param order - 被關閉的訂單                                      |
    //+------------------------------------------------------------------+
    virtual void onClose(const Order *order) override
    {
        m_orders.push(new OrderEvent(ORDER_TRACKED_CLOSED, order));
    }

    //+------------------------------------------------------------------+
    //| 訂單部分關閉時的回調函數                                          |
    //| 當訂單被部分關閉時觸發                                            |
    //| @param oldOrder - 部分關閉前的訂單狀態                           |
    //| @param newOrder - 部分關閉後的訂單狀態                           |
    //+------------------------------------------------------------------+
    virtual void onPartialClose(const Order *oldOrder, const Order *newOrder) override
    {
        m_orders.push(new OrderEvent(ORDER_TRACKED_PARTIALLY_CLOSED, newOrder));
    }

    //+------------------------------------------------------------------+
    //| 取得所有事件並填入指定的事件佇列                                  |
    //| 將內部存儲的所有事件複製到外部佇列中供處理                        |
    //| @param queue - 要填入事件的佇列引用                              |
    //| @return 事件的總數量                                             |
    //+------------------------------------------------------------------+
    int getEvents(OrderEventQueue& queue)
    {
        queue.clear();
        for (int i = 0; i < m_orders.size(); i++)
        {
            queue.Enqueue(m_orders[i]);
        }
        return queue.size();
    }
};